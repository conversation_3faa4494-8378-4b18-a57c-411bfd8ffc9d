import { Request, Response, NextFunction } from 'express';
import { Error as MongooseError } from 'mongoose';
import { config } from '@/config/environment';
import { AppError } from '@/utils/AppError';
import { IApiResponse } from '@/types';

// Handle Mongoose validation errors
const handleValidationError = (error: MongooseError.ValidationError): AppError => {
  const errors: Record<string, string> = {};
  
  Object.values(error.errors).forEach((err) => {
    if (err instanceof MongooseError.ValidatorError || err instanceof MongooseError.CastError) {
      errors[err.path] = err.message;
    }
  });

  const message = 'Validation failed';
  const appError = new AppError(message, 400);
  appError.errors = errors;
  return appError;
};

// Handle Mongoose duplicate key errors
const handleDuplicateKeyError = (error: any): AppError => {
  const field = Object.keys(error.keyValue)[0];
  const value = error.keyValue[field];
  const message = `${field.charAt(0).toUpperCase() + field.slice(1)} '${value}' already exists`;
  return new AppError(message, 409);
};

// Handle Mongoose cast errors
const handleCastError = (error: MongooseError.CastError): AppError => {
  const message = `Invalid ${error.path}: ${error.value}`;
  return new AppError(message, 400);
};

// Handle JWT errors
const handleJWTError = (): AppError => {
  return new AppError('Invalid token', 401);
};

const handleJWTExpiredError = (): AppError => {
  return new AppError('Token expired', 401);
};

// Send error response in development
const sendErrorDev = (err: AppError, res: Response): void => {
  const response: IApiResponse = {
    success: false,
    message: err.message,
    error: err.stack,
    errors: err.errors,
  };

  res.status(err.statusCode || 500).json(response);
};

// Send error response in production
const sendErrorProd = (err: AppError, res: Response): void => {
  // Operational, trusted error: send message to client
  if (err.isOperational) {
    const response: IApiResponse = {
      success: false,
      message: err.message,
      errors: err.errors,
    };

    res.status(err.statusCode || 500).json(response);
  } else {
    // Programming or other unknown error: don't leak error details
    console.error('ERROR 💥', err);

    const response: IApiResponse = {
      success: false,
      message: 'Something went wrong!',
    };

    res.status(500).json(response);
  }
};

export const errorHandler = (
  err: any,
  req: Request,
  res: Response,
  next: NextFunction
): void => {
  let error = { ...err };
  error.message = err.message;

  // Log error
  console.error('Error:', {
    message: err.message,
    stack: err.stack,
    url: req.url,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
  });

  // Mongoose validation error
  if (err.name === 'ValidationError') {
    error = handleValidationError(err);
  }

  // Mongoose duplicate key error
  if (err.code === 11000) {
    error = handleDuplicateKeyError(err);
  }

  // Mongoose cast error
  if (err.name === 'CastError') {
    error = handleCastError(err);
  }

  // JWT errors
  if (err.name === 'JsonWebTokenError') {
    error = handleJWTError();
  }

  if (err.name === 'TokenExpiredError') {
    error = handleJWTExpiredError();
  }

  // Ensure error has statusCode
  if (!error.statusCode) {
    error.statusCode = 500;
    error.isOperational = false;
  }

  // Send error response
  if (config.nodeEnv === 'development') {
    sendErrorDev(error, res);
  } else {
    sendErrorProd(error, res);
  }
};
