
import { create } from 'zustand';
import { Teacher, Classroom, Level, TimeSlot } from '@/lib/schemas';
import { scheduleApi } from '@/lib/api';
import { notify, withNotifications } from '@/lib/notifications';

interface StoreState {
  teachers: Teacher[];
  classrooms: Classroom[];
  levels: Level[];
  timeSlots: TimeSlot[];
  availableTimeSlots: string[];
  selectedTeacher: string | null;
  selectedClassroom: string | null;
  isLoading: boolean;

  // Actions
  setTeachers: (teachers: Teacher[]) => void;
  addTeacher: (teacher: Teacher) => void;
  updateTeacher: (id: string, teacher: Partial<Teacher>) => void;
  deleteTeacher: (id: string) => void;

  setClassrooms: (classrooms: Classroom[]) => void;
  addClassroom: (classroom: Classroom) => void;
  updateClassroom: (id: string, classroom: Partial<Classroom>) => void;
  deleteClassroom: (id: string) => void;

  setLevels: (levels: Level[]) => void;
  addLevel: (level: Level) => void;
  updateLevel: (id: string, level: Partial<Level>) => void;
  deleteLevel: (id: string) => void;

  setTimeSlots: (timeSlots: TimeSlot[]) => void;
  addTimeSlot: (timeSlot: TimeSlot) => void;
  updateTimeSlot: (id: string, timeSlot: Partial<TimeSlot>) => void;
  deleteTimeSlot: (id: string) => void;

  setAvailableTimeSlots: (timeSlots: string[]) => void;
  addAvailableTimeSlot: (timeSlot: string) => void;
  removeAvailableTimeSlot: (timeSlot: string) => void;

  setSelectedTeacher: (teacherId: string | null) => void;
  setSelectedClassroom: (classroomId: string | null) => void;

  // API Actions
  loadScheduleData: () => Promise<void>;
  saveScheduleData: () => Promise<void>;
  createTeacherAsync: (teacher: Omit<Teacher, 'id'>) => Promise<void>;
  updateTeacherAsync: (id: string, teacher: Partial<Teacher>) => Promise<void>;
  deleteTeacherAsync: (id: string) => Promise<void>;
  createClassroomAsync: (classroom: Omit<Classroom, 'id'>) => Promise<void>;
  updateClassroomAsync: (id: string, classroom: Partial<Classroom>) => Promise<void>;
  deleteClassroomAsync: (id: string) => Promise<void>;
  createLevelAsync: (level: Omit<Level, 'id'>) => Promise<void>;
  updateLevelAsync: (id: string, level: Partial<Level>) => Promise<void>;
  deleteLevelAsync: (id: string) => Promise<void>;
  createTimeSlotAsync: (timeSlot: Omit<TimeSlot, 'id'>) => Promise<void>;
  updateTimeSlotAsync: (id: string, timeSlot: Partial<TimeSlot>) => Promise<void>;
  deleteTimeSlotAsync: (id: string) => Promise<void>;

  // Utilities
  getFilteredTimeSlots: () => TimeSlot[];
  checkConflict: (newSlot: Omit<TimeSlot, 'id'>) => boolean;
}

export const useStore = create<StoreState>((set, get) => ({
  teachers: [],
  classrooms: [],
  levels: [],
  timeSlots: [],
  availableTimeSlots: [],
  selectedTeacher: null,
  selectedClassroom: null,
  isLoading: false,

  setTeachers: (teachers) => set({ teachers }),
  addTeacher: (teacher) => set((state) => ({ teachers: [...state.teachers, teacher] })),
  updateTeacher: (id, updates) =>
    set((state) => ({
      teachers: state.teachers.map((t) => (t.id === id ? { ...t, ...updates } : t)),
    })),
  deleteTeacher: (id) =>
    set((state) => ({
      teachers: state.teachers.filter((t) => t.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.teacherId !== id),
    })),

  setClassrooms: (classrooms) => set({ classrooms }),
  addClassroom: (classroom) => set((state) => ({ classrooms: [...state.classrooms, classroom] })),
  updateClassroom: (id, updates) =>
    set((state) => ({
      classrooms: state.classrooms.map((c) => (c.id === id ? { ...c, ...updates } : c)),
    })),
  deleteClassroom: (id) =>
    set((state) => ({
      classrooms: state.classrooms.filter((c) => c.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.classroomId !== id),
    })),

  setLevels: (levels) => set({ levels }),
  addLevel: (level) => set((state) => ({ levels: [...state.levels, level] })),
  updateLevel: (id, updates) =>
    set((state) => ({
      levels: state.levels.map((l) => (l.id === id ? { ...l, ...updates } : l)),
    })),
  deleteLevel: (id) =>
    set((state) => ({
      levels: state.levels.filter((l) => l.id !== id),
      timeSlots: state.timeSlots.filter((ts) => ts.levelId !== id),
    })),

  setTimeSlots: (timeSlots) => set({ timeSlots }),
  addTimeSlot: (timeSlot) => set((state) => ({ timeSlots: [...state.timeSlots, timeSlot] })),
  updateTimeSlot: (id, updates) =>
    set((state) => ({
      timeSlots: state.timeSlots.map((ts) => (ts.id === id ? { ...ts, ...updates } : ts)),
    })),
  deleteTimeSlot: (id) =>
    set((state) => ({
      timeSlots: state.timeSlots.filter((ts) => ts.id !== id),
    })),

  setAvailableTimeSlots: (timeSlots) => set({ availableTimeSlots: timeSlots }),
  addAvailableTimeSlot: (timeSlot) =>
    set((state) => ({
      availableTimeSlots: [...state.availableTimeSlots, timeSlot].sort()
    })),
  removeAvailableTimeSlot: (timeSlot) =>
    set((state) => ({
      availableTimeSlots: state.availableTimeSlots.filter((ts) => ts !== timeSlot),
    })),

  setSelectedTeacher: (teacherId) => set({ selectedTeacher: teacherId }),
  setSelectedClassroom: (classroomId) => set({ selectedClassroom: classroomId }),

  // API Actions
  loadScheduleData: async () => {
    try {
      set({ isLoading: true });
      const data = await scheduleApi.getScheduleData();
      set({
        teachers: data.teachers,
        classrooms: data.classrooms,
        levels: data.levels,
        timeSlots: data.timeSlots,
        isLoading: false,
      });
      notify.success('Schedule data loaded successfully');
    } catch (error) {
      set({ isLoading: false });
      // Don't show error notification for initial load failure
      // The app should work offline with local state
      console.warn('Could not load data from server, using local state:', error);
    }
  },

  saveScheduleData: async () => {
    const { teachers, classrooms, levels, timeSlots } = get();
    await withNotifications(
      async () => {
        await scheduleApi.saveSchedule({
          teachers,
          classrooms,
          levels,
          timeSlots,
        });
      },
      {
        loading: 'Saving schedule...',
        success: 'Schedule saved successfully',
        errorPrefix: 'Failed to save schedule',
      }
    );
  },

  createTeacherAsync: async (teacher) => {
    const result = await withNotifications(
      async () => {
        const newTeacher = await scheduleApi.createTeacher(teacher);
        set((state) => ({ teachers: [...state.teachers, newTeacher] }));
        return newTeacher;
      },
      {
        loading: 'Adding teacher...',
        success: 'Teacher added successfully',
        errorPrefix: 'Failed to add teacher',
      }
    );
  },

  updateTeacherAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedTeacher = await scheduleApi.updateTeacher(id, updates);
        set((state) => ({
          teachers: state.teachers.map((t) => (t.id === id ? updatedTeacher : t)),
        }));
      },
      {
        loading: 'Updating teacher...',
        success: 'Teacher updated successfully',
        errorPrefix: 'Failed to update teacher',
      }
    );
  },

  deleteTeacherAsync: async (id) => {
    await withNotifications(
      async () => {
        await scheduleApi.deleteTeacher(id);
        set((state) => ({
          teachers: state.teachers.filter((t) => t.id !== id),
          timeSlots: state.timeSlots.filter((ts) => ts.teacherId !== id),
        }));
      },
      {
        loading: 'Removing teacher...',
        success: 'Teacher removed successfully',
        errorPrefix: 'Failed to remove teacher',
      }
    );
  },

  createClassroomAsync: async (classroom) => {
    await withNotifications(
      async () => {
        const newClassroom = await scheduleApi.createClassroom(classroom);
        set((state) => ({ classrooms: [...state.classrooms, newClassroom] }));
      },
      {
        loading: 'Adding classroom...',
        success: 'Classroom added successfully',
        errorPrefix: 'Failed to add classroom',
      }
    );
  },

  updateClassroomAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedClassroom = await scheduleApi.updateClassroom(id, updates);
        set((state) => ({
          classrooms: state.classrooms.map((c) => (c.id === id ? updatedClassroom : c)),
        }));
      },
      {
        loading: 'Updating classroom...',
        success: 'Classroom updated successfully',
        errorPrefix: 'Failed to update classroom',
      }
    );
  },

  deleteClassroomAsync: async (id) => {
    await withNotifications(
      async () => {
        await scheduleApi.deleteClassroom(id);
        set((state) => ({
          classrooms: state.classrooms.filter((c) => c.id !== id),
          timeSlots: state.timeSlots.filter((ts) => ts.classroomId !== id),
        }));
      },
      {
        loading: 'Removing classroom...',
        success: 'Classroom removed successfully',
        errorPrefix: 'Failed to remove classroom',
      }
    );
  },

  createLevelAsync: async (level) => {
    await withNotifications(
      async () => {
        const newLevel = await scheduleApi.createLevel(level);
        set((state) => ({ levels: [...state.levels, newLevel] }));
      },
      {
        loading: 'Adding level...',
        success: 'Level added successfully',
        errorPrefix: 'Failed to add level',
      }
    );
  },

  updateLevelAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedLevel = await scheduleApi.updateLevel(id, updates);
        set((state) => ({
          levels: state.levels.map((l) => (l.id === id ? updatedLevel : l)),
        }));
      },
      {
        loading: 'Updating level...',
        success: 'Level updated successfully',
        errorPrefix: 'Failed to update level',
      }
    );
  },

  deleteLevelAsync: async (id) => {
    await withNotifications(
      async () => {
        await scheduleApi.deleteLevel(id);
        set((state) => ({
          levels: state.levels.filter((l) => l.id !== id),
          timeSlots: state.timeSlots.filter((ts) => ts.levelId !== id),
        }));
      },
      {
        loading: 'Removing level...',
        success: 'Level removed successfully',
        errorPrefix: 'Failed to remove level',
      }
    );
  },

  createTimeSlotAsync: async (timeSlot) => {
    await withNotifications(
      async () => {
        const newTimeSlot = await scheduleApi.createTimeSlot(timeSlot);
        set((state) => ({ timeSlots: [...state.timeSlots, newTimeSlot] }));
      },
      {
        loading: 'Adding time slot...',
        success: 'Time slot added successfully',
        errorPrefix: 'Failed to add time slot',
      }
    );
  },

  updateTimeSlotAsync: async (id, updates) => {
    await withNotifications(
      async () => {
        const updatedTimeSlot = await scheduleApi.updateTimeSlot(id, updates);
        set((state) => ({
          timeSlots: state.timeSlots.map((ts) => (ts.id === id ? updatedTimeSlot : ts)),
        }));
      },
      {
        loading: 'Updating time slot...',
        success: 'Time slot updated successfully',
        errorPrefix: 'Failed to update time slot',
      }
    );
  },

  deleteTimeSlotAsync: async (id) => {
    await withNotifications(
      async () => {
        await scheduleApi.deleteTimeSlot(id);
        set((state) => ({
          timeSlots: state.timeSlots.filter((ts) => ts.id !== id),
        }));
      },
      {
        loading: 'Removing time slot...',
        success: 'Time slot removed successfully',
        errorPrefix: 'Failed to remove time slot',
      }
    );
  },

  getFilteredTimeSlots: () => {
    const { timeSlots, selectedTeacher, selectedClassroom } = get();
    return timeSlots.filter((slot) => {
      if (selectedTeacher && slot.teacherId !== selectedTeacher) return false;
      if (selectedClassroom && slot.classroomId !== selectedClassroom) return false;
      return true;
    });
  },

  checkConflict: (newSlot) => {
    const { timeSlots } = get();
    return timeSlots.some((slot) => {
      if (slot.dayOfWeek !== newSlot.dayOfWeek) return false;
      
      const slotStart = new Date(`2024-01-01 ${slot.start}`);
      const slotEnd = new Date(`2024-01-01 ${slot.end}`);
      const newStart = new Date(`2024-01-01 ${newSlot.start}`);
      const newEnd = new Date(`2024-01-01 ${newSlot.end}`);
      
      const timeOverlap = newStart < slotEnd && newEnd > slotStart;
      
      if (!timeOverlap) return false;
      
      // Only conflict if same teacher OR same classroom is being used
      return slot.teacherId === newSlot.teacherId || slot.classroomId === newSlot.classroomId;
    });
  },
}));
