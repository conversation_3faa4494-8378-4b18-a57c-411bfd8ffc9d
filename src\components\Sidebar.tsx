
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Plus, Users, MapPin, BookOpen, Filter, Download } from 'lucide-react';
import { EntityModal } from './EntityModal';
import { ExportModal } from './ExportModal';

export const Sidebar: React.FC = () => {
  const {
    teachers,
    classrooms,
    levels,
    selectedTeacher,
    selectedClassroom,
    setSelectedTeacher,
    setSelectedClassroom,
  } = useStore();

  const [entityModalOpen, setEntityModalOpen] = useState(false);
  const [entityType, setEntityType] = useState<'teacher' | 'classroom' | 'level'>('teacher');
  const [exportModalOpen, setExportModalOpen] = useState(false);

  const openEntityModal = (type: 'teacher' | 'classroom' | 'level') => {
    setEntityType(type);
    setEntityModalOpen(true);
  };

  return (
    <motion.div
      initial={{ x: -300, opacity: 0 }}
      animate={{ x: 0, opacity: 1 }}
      className="w-80 h-full glass-card p-6 liquid-container"
    >
      <div className="liquid-background absolute inset-0 opacity-20"></div>
      
      <div className="relative z-10 h-full flex flex-col">
        <div className="mb-6">
          <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
            School Timetable
          </h1>
          <p className="text-sm text-muted-foreground mt-1">Admin Dashboard</p>
        </div>

        <ScrollArea className="flex-1">
          {/* Filters */}
          <div className="mb-6">
            <div className="flex items-center mb-3">
              <Filter className="h-4 w-4 mr-2" />
              <h3 className="font-semibold">Filters</h3>
            </div>
            <div className="space-y-2">
              <Button
                variant={selectedTeacher ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedTeacher(selectedTeacher ? null : teachers[0]?.id)}
                className="w-full glass-button justify-start"
              >
                Filter by Teacher
              </Button>
              <Button
                variant={selectedClassroom ? "default" : "outline"}
                size="sm"
                onClick={() => setSelectedClassroom(selectedClassroom ? null : classrooms[0]?.id)}
                className="w-full glass-button justify-start"
              >
                Filter by Classroom
              </Button>
            </div>
          </div>

          <Separator className="my-4" />

          {/* Teachers */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <Users className="h-4 w-4 mr-2" />
                <h3 className="font-semibold">Teachers</h3>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => openEntityModal('teacher')}
                className="h-6 w-6 p-0 glass-button"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2">
              {teachers.map((teacher) => (
                <motion.div
                  key={teacher.id}
                  whileHover={{ scale: 1.02 }}
                  className={`p-2 rounded-lg cursor-pointer transition-all shine-effect ${
                    selectedTeacher === teacher.id
                      ? 'bg-glass-light border border-white/30'
                      : 'hover:bg-glass-dark'
                  }`}
                  onClick={() => setSelectedTeacher(selectedTeacher === teacher.id ? null : teacher.id)}
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: teacher.color }}
                    />
                    <span className="text-sm font-medium">{teacher.name}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Classrooms */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <MapPin className="h-4 w-4 mr-2" />
                <h3 className="font-semibold">Classrooms</h3>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => openEntityModal('classroom')}
                className="h-6 w-6 p-0 glass-button"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2">
              {classrooms.map((classroom) => (
                <motion.div
                  key={classroom.id}
                  whileHover={{ scale: 1.02 }}
                  className={`p-2 rounded-lg cursor-pointer transition-all shine-effect ${
                    selectedClassroom === classroom.id
                      ? 'bg-glass-light border border-white/30'
                      : 'hover:bg-glass-dark'
                  }`}
                  onClick={() => setSelectedClassroom(selectedClassroom === classroom.id ? null : classroom.id)}
                >
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">{classroom.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {classroom.capacity}
                    </Badge>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Levels */}
          <div className="mb-6">
            <div className="flex items-center justify-between mb-3">
              <div className="flex items-center">
                <BookOpen className="h-4 w-4 mr-2" />
                <h3 className="font-semibold">Levels</h3>
              </div>
              <Button
                size="sm"
                variant="ghost"
                onClick={() => openEntityModal('level')}
                className="h-6 w-6 p-0 glass-button"
              >
                <Plus className="h-3 w-3" />
              </Button>
            </div>
            <div className="space-y-2">
              {levels.map((level) => (
                <motion.div
                  key={level.id}
                  whileHover={{ scale: 1.02 }}
                  className="p-2 rounded-lg hover:bg-glass-dark transition-all shine-effect"
                >
                  <div className="flex items-center space-x-2">
                    <div
                      className="w-3 h-3 rounded-full"
                      style={{ backgroundColor: level.color }}
                    />
                    <span className="text-sm font-medium">{level.label}</span>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </ScrollArea>

        {/* Export Button */}
        <div className="mt-4">
          <Button
            onClick={() => setExportModalOpen(true)}
            className="w-full glass-button bg-gradient-to-r from-blue-500/20 to-purple-500/20 hover:from-blue-500/30 hover:to-purple-500/30"
          >
            <Download className="h-4 w-4 mr-2" />
            Export Timetable
          </Button>
        </div>
      </div>

      <EntityModal
        isOpen={entityModalOpen}
        onClose={() => setEntityModalOpen(false)}
        entityType={entityType}
      />

      <ExportModal
        isOpen={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
      />
    </motion.div>
  );
};
