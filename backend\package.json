{"name": "weekly-schedule-backend", "version": "1.0.0", "description": "Backend API for Weekly Schedule Application", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon src/server.ts", "build": "tsc", "build:watch": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "keywords": ["nodejs", "express", "mongodb", "typescript", "api", "schedule"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^8.0.3", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "joi": "^17.11.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "cookie-parser": "^1.4.6", "express-validator": "^7.0.1", "zod": "^3.22.4"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.4", "@types/cors": "^2.8.17", "@types/morgan": "^1.9.9", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.5", "@types/compression": "^1.7.5", "@types/cookie-parser": "^1.4.6", "@types/jest": "^29.5.8", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "rimraf": "^5.0.5", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}