import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { AppError } from './AppError';

// User validation schemas
export const registerSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().min(8).required().messages({
    'string.min': 'Password must be at least 8 characters long',
    'any.required': 'Password is required',
  }),
  firstName: Joi.string().trim().max(50).required().messages({
    'string.max': 'First name cannot exceed 50 characters',
    'any.required': 'First name is required',
  }),
  lastName: Joi.string().trim().max(50).required().messages({
    'string.max': 'Last name cannot exceed 50 characters',
    'any.required': 'Last name is required',
  }),
  avatar: Joi.string().uri().optional(),
});

export const loginSchema = Joi.object({
  email: Joi.string().email().required().messages({
    'string.email': 'Please provide a valid email address',
    'any.required': 'Email is required',
  }),
  password: Joi.string().required().messages({
    'any.required': 'Password is required',
  }),
});

// Schedule validation schemas
export const createScheduleSchema = Joi.object({
  name: Joi.string().trim().max(100).required().messages({
    'string.max': 'Schedule name cannot exceed 100 characters',
    'any.required': 'Schedule name is required',
  }),
  description: Joi.string().trim().max(500).optional(),
  startDate: Joi.date().required().messages({
    'any.required': 'Start date is required',
  }),
  endDate: Joi.date().greater(Joi.ref('startDate')).required().messages({
    'date.greater': 'End date must be after start date',
    'any.required': 'End date is required',
  }),
  isDefault: Joi.boolean().optional(),
});

export const updateScheduleSchema = Joi.object({
  name: Joi.string().trim().max(100).optional(),
  description: Joi.string().trim().max(500).optional(),
  startDate: Joi.date().optional(),
  endDate: Joi.date().when('startDate', {
    is: Joi.exist(),
    then: Joi.date().greater(Joi.ref('startDate')).required(),
    otherwise: Joi.date().optional(),
  }),
  isDefault: Joi.boolean().optional(),
});

// Task validation schemas
export const createTaskSchema = Joi.object({
  title: Joi.string().trim().max(200).required().messages({
    'string.max': 'Title cannot exceed 200 characters',
    'any.required': 'Task title is required',
  }),
  description: Joi.string().trim().max(1000).optional(),
  startTime: Joi.date().required().messages({
    'any.required': 'Start time is required',
  }),
  endTime: Joi.date().greater(Joi.ref('startTime')).required().messages({
    'date.greater': 'End time must be after start time',
    'any.required': 'End time is required',
  }),
  priority: Joi.string().valid('low', 'medium', 'high').optional(),
  status: Joi.string().valid('pending', 'in-progress', 'completed', 'cancelled').optional(),
  category: Joi.string().trim().max(50).optional(),
  color: Joi.string().pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/).optional().messages({
    'string.pattern.base': 'Please provide a valid hex color',
  }),
  isRecurring: Joi.boolean().optional(),
  recurringPattern: Joi.when('isRecurring', {
    is: true,
    then: Joi.object({
      type: Joi.string().valid('daily', 'weekly', 'monthly').required(),
      interval: Joi.number().integer().min(1).required(),
      daysOfWeek: Joi.array().items(Joi.number().integer().min(0).max(6)).optional(),
      endDate: Joi.date().optional(),
    }).required(),
    otherwise: Joi.optional(),
  }),
  scheduleId: Joi.string().required().messages({
    'any.required': 'Schedule ID is required',
  }),
});

export const updateTaskSchema = Joi.object({
  title: Joi.string().trim().max(200).optional(),
  description: Joi.string().trim().max(1000).optional(),
  startTime: Joi.date().optional(),
  endTime: Joi.date().when('startTime', {
    is: Joi.exist(),
    then: Joi.date().greater(Joi.ref('startTime')).required(),
    otherwise: Joi.date().optional(),
  }),
  priority: Joi.string().valid('low', 'medium', 'high').optional(),
  status: Joi.string().valid('pending', 'in-progress', 'completed', 'cancelled').optional(),
  category: Joi.string().trim().max(50).optional(),
  color: Joi.string().pattern(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/).optional(),
  isRecurring: Joi.boolean().optional(),
  recurringPattern: Joi.when('isRecurring', {
    is: true,
    then: Joi.object({
      type: Joi.string().valid('daily', 'weekly', 'monthly').required(),
      interval: Joi.number().integer().min(1).required(),
      daysOfWeek: Joi.array().items(Joi.number().integer().min(0).max(6)).optional(),
      endDate: Joi.date().optional(),
    }).optional(),
    otherwise: Joi.optional(),
  }),
});

// Validation middleware factory
export const validate = (schema: Joi.ObjectSchema) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    const { error, value } = schema.validate(req.body, {
      abortEarly: false,
      stripUnknown: true,
    });

    if (error) {
      const errors: Record<string, string> = {};
      error.details.forEach((detail) => {
        const key = detail.path.join('.');
        errors[key] = detail.message;
      });

      const appError = new AppError('Validation failed', 400);
      appError.errors = errors;
      return next(appError);
    }

    req.body = value;
    next();
  };
};
