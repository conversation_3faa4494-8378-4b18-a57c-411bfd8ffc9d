
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useStore } from '@/store/useStore';
import { Teacher, Classroom, Level } from '@/lib/schemas';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { toast } from '@/hooks/use-toast';

interface EntityModalProps {
  isOpen: boolean;
  onClose: () => void;
  entityType: 'teacher' | 'classroom' | 'level';
}

export const EntityModal: React.FC<EntityModalProps> = ({ isOpen, onClose, entityType }) => {
  const { addTeacher, addClassroom, addLevel } = useStore();
  
  const [formData, setFormData] = useState({
    name: '',
    color: '#4F46E5',
    capacity: 20,
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      const id = Date.now().toString();
      
      switch (entityType) {
        case 'teacher':
          const teacher: Teacher = {
            id,
            name: formData.name,
            color: formData.color,
          };
          addTeacher(teacher);
          break;
          
        case 'classroom':
          const classroom: Classroom = {
            id,
            name: formData.name,
            capacity: formData.capacity,
          };
          addClassroom(classroom);
          break;
          
        case 'level':
          const level: Level = {
            id,
            label: formData.name,
            color: formData.color,
          };
          addLevel(level);
          break;
      }

      toast({
        title: "Success",
        description: `${entityType.charAt(0).toUpperCase() + entityType.slice(1)} has been created.`,
      });

      setFormData({ name: '', color: '#4F46E5', capacity: 20 });
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Please fill in all required fields.",
        variant: "destructive",
      });
    }
  };

  const getTitle = () => {
    return `Add New ${entityType.charAt(0).toUpperCase() + entityType.slice(1)}`;
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="glass-card border-white/20">
        <DialogHeader>
          <DialogTitle className="text-xl font-semibold">{getTitle()}</DialogTitle>
        </DialogHeader>

        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          onSubmit={handleSubmit}
          className="space-y-4"
        >
          <div className="space-y-2">
            <Label htmlFor="name">
              {entityType === 'level' ? 'Label' : 'Name'}
            </Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              placeholder={`Enter ${entityType} ${entityType === 'level' ? 'label' : 'name'}`}
              className="glass-button"
              required
            />
          </div>

          {entityType !== 'classroom' && (
            <div className="space-y-2">
              <Label htmlFor="color">Color</Label>
              <div className="flex space-x-2">
                <Input
                  id="color"
                  type="color"
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  className="w-16 h-10 glass-button"
                />
                <Input
                  value={formData.color}
                  onChange={(e) => setFormData({ ...formData, color: e.target.value })}
                  placeholder="#4F46E5"
                  className="glass-button"
                />
              </div>
            </div>
          )}

          {entityType === 'classroom' && (
            <div className="space-y-2">
              <Label htmlFor="capacity">Capacity</Label>
              <Input
                id="capacity"
                type="number"
                value={formData.capacity}
                onChange={(e) => setFormData({ ...formData, capacity: parseInt(e.target.value) || 20 })}
                min={1}
                className="glass-button"
                required
              />
            </div>
          )}

          <div className="flex justify-end space-x-2">
            <Button type="button" variant="outline" onClick={onClose} className="glass-button">
              Cancel
            </Button>
            <Button type="submit" className="glass-button bg-primary/20 hover:bg-primary/30">
              Create
            </Button>
          </div>
        </motion.form>
      </DialogContent>
    </Dialog>
  );
};
